// Copyright © 2024 Super State Studio. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Curves/CurveVector.h"
#include "ProceduralWeaponAnimation.generated.h"

/**
 * Enum defining animation states for weapon procedural animation
 */
UENUM(BlueprintType)
enum class EWeaponAnimState : uint8
{
	Idle,
	Walking,
	Sprinting,
	Jumping,
	Crouching,
	ADS,
};

/**
 * Struct to hold animation curve data
 */
USTRUCT(BlueprintType)
struct FWeaponAnimData
{
	GENERATED_BODY()

	// The curve asset containing location animation data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	UCurveVector* LocationCurve = nullptr;

	// The curve asset containing rotation animation data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	UCurveVector* RotationCurve = nullptr;

	// Duration of the animation in seconds
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float Duration = 1.0f;

	// Playback rate multiplier
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float PlaybackRate = 1.0f;

	// Whether the animation should loop
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	bool bLooping = true;

	// Whether this animation should be additive (added on top of the current animation)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	bool bAdditive = false;

	FWeaponAnimData()
	{
	}
};

/**
 * Struct to hold blend state information
 */
USTRUCT(BlueprintType)
struct FWeaponAnimBlendState
{
	GENERATED_BODY()

	// Current animation state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	EWeaponAnimState CurrentState = EWeaponAnimState::Idle;

	// Target animation state for blending
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	EWeaponAnimState TargetState = EWeaponAnimState::Idle;

	// Current blend alpha (0.0 = current state, 1.0 = target state)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float BlendAlpha = 0.0f;

	// Total time for the blend to complete
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float BlendTime = 0.2f;

	// Current time within the blend
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float CurrentBlendTime = 0.0f;

	// Current time within the current animation
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float CurrentAnimTime = 0.0f;

	// Current time within the target animation
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float TargetAnimTime = 0.0f;

	// Marks the state to be removed
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	bool bIsMarkedRemove = false;

	// Virtual transform used when blending from an interrupted blend state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	FTransform VirtualTransform = FTransform::Identity;

	// Whether we're using a virtual transform as the current state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	bool bUsingVirtualTransform = false;

	FWeaponAnimBlendState()
	{
	}
};

/**
 * Component that handles procedural animation of a weapon control mesh
 * using curve vectors and blending between different animation states.
 * Provides a transform output rather than directly applying it.
 */
UCLASS(Abstract, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class QUANTOMB_API UProceduralWeaponAnimation : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UProceduralWeaponAnimation();

	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType,
	                           FActorComponentTickFunction* ThisTickFunction) override;

	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	// Set the current animation state with optional blend time
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	void SetAnimationState(EWeaponAnimState NewState, float BlendTime = 1.0f);

	// Add an additive animation state
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	void AddAdditiveState(EWeaponAnimState AdditiveState, float BlendInTime = 1.0f);

	// Remove an additive animation state
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	void RemoveAdditiveState(EWeaponAnimState AdditiveState, float BlendOutTime = 1.0f);

	// Get the current animation state
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	EWeaponAnimState GetCurrentAnimationState() const;

	// Get the target animation state (during blending)
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	EWeaponAnimState GetTargetAnimationState() const;

	// Check if an additive state is active
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	bool IsAdditiveStateActive(EWeaponAnimState State) const;

	// Get the calculated transform for the current frame
	UFUNCTION(BlueprintCallable, Category = "Weapon Animation")
	FTransform GetCalculatedTransform() const;

protected:
	// Map of animation states to animation data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	TMap<EWeaponAnimState, FWeaponAnimData> AnimationMap;

	// Current blend state
	UPROPERTY(BlueprintReadOnly, Category = "Animation")
	FWeaponAnimBlendState BlendState;

	// Currently active additive states
	UPROPERTY(BlueprintReadOnly, Category = "Animation")
	TMap<EWeaponAnimState, FWeaponAnimBlendState> ActiveAdditiveStates;

	// Default blend time between states
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float DefaultBlendTime = 1.0f;

private:
	// The calculated transform for the current frame
	FTransform CalculatedTransform;

	// Update the blend state
	void UpdateBlendState(float DeltaTime);

	// Update additive states
	void UpdateAdditiveStates(float DeltaTime);

	// Calculate the final transform
	void CalculateTransform();

	// Sample a curve at a specific time
	FTransform SampleCurve(const FWeaponAnimData& AnimData, float Time);

	// Get the current visual transform (what the user actually sees)
	FTransform GetCurrentVisualTransform();
};
