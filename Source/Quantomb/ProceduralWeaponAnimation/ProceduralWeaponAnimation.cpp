// Copyright © 2024 Super State Studio. All Rights Reserved.

#include "Quantomb/ProceduralWeaponAnimation/ProceduralWeaponAnimation.h"
#include "Kismet/KismetMathLibrary.h"

// Sets default values for this component's properties
UProceduralWeaponAnimation::UProceduralWeaponAnimation()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickGroup = TG_PostPhysics;

	// Initialize default values
	CalculatedTransform = FTransform::Identity;
}

// Called when the game starts
void UProceduralWeaponAnimation::BeginPlay()
{
	Super::BeginPlay();

	// Initialize blend state
	BlendState.CurrentState = EWeaponAnimState::Idle;
	BlendState.TargetState = EWeaponAnimState::Idle;
	BlendState.BlendAlpha = 0.0f;
	BlendState.BlendTime = DefaultBlendTime;
	BlendState.CurrentBlendTime = 0.0f;
	BlendState.CurrentAnimTime = 0.0f;
	BlendState.TargetAnimTime = 0.0f;
}

// Called every frame
void UProceduralWeaponAnimation::TickComponent(float DeltaTime, ELevelTick TickType,
                                               FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// Update blend state
	UpdateBlendState(DeltaTime);

	// Update additive states
	UpdateAdditiveStates(DeltaTime);

	// Calculate the final transform
	CalculateTransform();
}

void UProceduralWeaponAnimation::SetAnimationState(EWeaponAnimState NewState, float BlendTime)
{
	// Don't blend if already in this state
	if (BlendState.CurrentState == NewState && BlendState.BlendAlpha == 0.0f)
	{
		return;
	}

	// If we're already blending to this state, just continue
	if (BlendState.TargetState == NewState)
	{
		return;
	}

	/*// If we're currently blending to a different state, update current state to reflect the blend progress
	if (BlendState.BlendAlpha > 0.0f)
	{
		// Sample current animation curve
		if (const FWeaponAnimData* CurrentAnimData = AnimationMap.Find(BlendState.CurrentState))
		{
			FTransform CurrentTransform = SampleCurve(*CurrentAnimData, BlendState.CurrentAnimTime);

			// Sample target animation curve
			if (const FWeaponAnimData* TargetAnimData = AnimationMap.Find(BlendState.TargetState))
			{
				FTransform TargetTransform = SampleCurve(*TargetAnimData, BlendState.TargetAnimTime);

				// Blend between current and target transforms based on current blend alpha
				FTransform BlendedTransform = UKismetMathLibrary::TLerp(CurrentTransform, TargetTransform, BlendState.BlendAlpha);

				// Update current state to be the blended state
				BlendState.CurrentState = BlendState.TargetState;
				BlendState.CurrentAnimTime = BlendState.TargetAnimTime;
			}
		}
	}*/

	// If we're currently blending and want to change to a new state before the blend completes
	if (BlendState.BlendAlpha > 0.0f && BlendState.BlendAlpha < 1.0f)
	{
		// Check if we're reversing back to the original state
		if (BlendState.CurrentState == NewState)
		{
			// Seamless reversal: swap states and reverse the blend
			EWeaponAnimState TempState = BlendState.CurrentState;
			float TempAnimTime = BlendState.CurrentAnimTime;

			BlendState.CurrentState = BlendState.TargetState;
			BlendState.CurrentAnimTime = BlendState.TargetAnimTime;
			BlendState.TargetState = TempState;
			BlendState.TargetAnimTime = TempAnimTime;

			// Reverse the blend alpha and adjust timing for seamless transition
			float RemainingBlendAlpha = 1.0f - BlendState.BlendAlpha;
			BlendState.BlendAlpha = RemainingBlendAlpha;
			BlendState.BlendTime = BlendTime > 0.0f ? BlendTime : DefaultBlendTime;
			BlendState.CurrentBlendTime = BlendState.BlendTime * RemainingBlendAlpha;
		}
		else
		{
			// Blending to a completely new state - preserve the current blended position
			// Calculate the current blended transform to use as our virtual starting point
			FTransform CurrentTransform = FTransform::Identity;
			FTransform TargetTransform = FTransform::Identity;

			if (const FWeaponAnimData* CurrentAnimData = AnimationMap.Find(BlendState.CurrentState))
			{
				CurrentTransform = SampleCurve(*CurrentAnimData, BlendState.CurrentAnimTime);
			}

			if (const FWeaponAnimData* TargetAnimData = AnimationMap.Find(BlendState.TargetState))
			{
				TargetTransform = SampleCurve(*TargetAnimData, BlendState.TargetAnimTime);
			}

			// Calculate the blended transform at current progress - this becomes our virtual starting point
			BlendState.VirtualTransform = UKismetMathLibrary::TLerp(CurrentTransform, TargetTransform, BlendState.BlendAlpha);
			BlendState.bUsingVirtualTransform = true;

			// Set up new blend from virtual transform to the requested state
			BlendState.CurrentState = EWeaponAnimState::Idle; // Doesn't matter since we're using virtual transform
			BlendState.TargetState = NewState;
			BlendState.BlendTime = BlendTime > 0.0f ? BlendTime : DefaultBlendTime;
			BlendState.CurrentBlendTime = 0.0f;
			BlendState.BlendAlpha = 0.0f;
			BlendState.TargetAnimTime = 0.0f;
		}
	}
	else
	{
		// No active blend or blend is complete - set up new blend normally
		BlendState.TargetState = NewState;
		BlendState.BlendTime = BlendTime > 0.0f ? BlendTime : DefaultBlendTime;
		BlendState.CurrentBlendTime = 0.0f;
		BlendState.BlendAlpha = 0.0f;
		BlendState.TargetAnimTime = 0.0f; // Start target animation from beginning
	}
}

void UProceduralWeaponAnimation::UpdateBlendState(float DeltaTime)
{
	// Update animation times
	if (const FWeaponAnimData* CurrentAnimData = AnimationMap.Find(BlendState.CurrentState))
	{
		// Update current animation time
		BlendState.CurrentAnimTime += DeltaTime * CurrentAnimData->PlaybackRate;

		// Handle looping
		if (CurrentAnimData->bLooping && BlendState.CurrentAnimTime > CurrentAnimData->Duration)
		{
			BlendState.CurrentAnimTime = FMath::Fmod(BlendState.CurrentAnimTime, CurrentAnimData->Duration);
		}
		else if (!CurrentAnimData->bLooping)
		{
			BlendState.CurrentAnimTime = FMath::Min(BlendState.CurrentAnimTime, CurrentAnimData->Duration);
		}
	}

	if (const FWeaponAnimData* TargetAnimData = AnimationMap.Find(BlendState.TargetState))
	{
		// Update target animation time
		BlendState.TargetAnimTime += DeltaTime * TargetAnimData->PlaybackRate;

		// Handle looping
		if (TargetAnimData->bLooping && BlendState.TargetAnimTime > TargetAnimData->Duration)
		{
			BlendState.TargetAnimTime = FMath::Fmod(BlendState.TargetAnimTime, TargetAnimData->Duration);
		}
		else if (!TargetAnimData->bLooping)
		{
			BlendState.TargetAnimTime = FMath::Min(BlendState.TargetAnimTime, TargetAnimData->Duration);
		}
	}

	// Update blend if we're blending between states
	if (BlendState.CurrentState != BlendState.TargetState)
	{
		// Update blend time
		BlendState.CurrentBlendTime += DeltaTime;

		UE_LOG(LogTemp, Display, TEXT("Current blend time: %f"), BlendState.CurrentBlendTime);
		UE_LOG(LogTemp, Display, TEXT("Blend time: %f"), BlendState.BlendTime);
		UE_LOG(LogTemp, Display, TEXT("Blend alpha: %f"), BlendState.BlendAlpha);

		// Calculate blend alpha (0.0 to 1.0)
		BlendState.BlendAlpha = FMath::Clamp(BlendState.CurrentBlendTime / BlendState.BlendTime, 0.0f, 1.0f);

		// If blend is complete, update current state
		if (BlendState.BlendAlpha >= 1.0f)
		{
			BlendState.CurrentState = BlendState.TargetState;
			BlendState.CurrentAnimTime = BlendState.TargetAnimTime;
			BlendState.BlendAlpha = 0.0f;
		}
	}

}

void UProceduralWeaponAnimation::AddAdditiveState(EWeaponAnimState AdditiveState, float BlendInTime)
{
	// Check if the animation data exists and is marked as additive
	if (const FWeaponAnimData* AnimData = AnimationMap.Find(AdditiveState))
	{
		if (!AnimData->bAdditive)
		{
			UE_LOG(LogTemp, Warning, TEXT("Attempted to add non-additive animation state as additive: %d"),
			       (int32)AdditiveState);
			return;
		}

		// If already active, just reset blend if needed
		if (ActiveAdditiveStates.Contains(AdditiveState))
		{
			FWeaponAnimBlendState& ExistingState = ActiveAdditiveStates[AdditiveState];

			// If blending out, reverse the blend
			if (ExistingState.BlendAlpha < 1.0f)
			{
				ExistingState.BlendTime = BlendInTime;
				ExistingState.CurrentBlendTime = ExistingState.BlendTime * (1.0f - ExistingState.BlendAlpha);
				ExistingState.TargetState = AdditiveState; // Target is self for additive states
			}

			return;
		}

		// Create new additive state
		FWeaponAnimBlendState NewAdditiveState;
		NewAdditiveState.CurrentState = AdditiveState;
		NewAdditiveState.TargetState = AdditiveState; // Target is self for additive states
		NewAdditiveState.BlendAlpha = 0.0f;
		NewAdditiveState.BlendTime = BlendInTime > 0.0f ? BlendInTime : DefaultBlendTime;
		NewAdditiveState.CurrentBlendTime = 0.0f;
		NewAdditiveState.CurrentAnimTime = 0.0f;
		NewAdditiveState.TargetAnimTime = 0.0f;

		// Add to active additive states
		ActiveAdditiveStates.Add(AdditiveState, NewAdditiveState);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Attempted to add additive state with no animation data: %d"),
		       (int32)AdditiveState);
	}
}

void UProceduralWeaponAnimation::RemoveAdditiveState(EWeaponAnimState AdditiveState, float BlendOutTime)
{
	// Check if the additive state is active
	if (ActiveAdditiveStates.Contains(AdditiveState))
	{
		FWeaponAnimBlendState& ExistingState = ActiveAdditiveStates[AdditiveState];

		// If already blending out and almost done, just remove it
		if (ExistingState.BlendAlpha < 0.1f)
		{
			ActiveAdditiveStates.Remove(AdditiveState);
			return;
		}

		if (!ExistingState.bIsMarkedRemove)
		{
			// Set up blend out
			ExistingState.bIsMarkedRemove = true;
			ExistingState.BlendTime = BlendOutTime > 0.0f ? BlendOutTime : DefaultBlendTime;
		}

		// We'll remove it when blend is complete in UpdateAdditiveStates()
	}
}

void UProceduralWeaponAnimation::UpdateAdditiveStates(float DeltaTime)
{
	// States to remove after iteration
	TArray<EWeaponAnimState> StatesToRemove;

	// Update each additive state
	for (auto& Pair : ActiveAdditiveStates)
	{
		EWeaponAnimState AdditiveState = Pair.Key;
		FWeaponAnimBlendState& AdditiveBlendState = Pair.Value;

		if (const FWeaponAnimData* AdditiveAnimData = AnimationMap.Find(AdditiveState))
		{
			// Update animation time
			AdditiveBlendState.CurrentAnimTime += DeltaTime * AdditiveAnimData->PlaybackRate;

			// Handle looping
			if (AdditiveAnimData->bLooping && AdditiveBlendState.CurrentAnimTime > AdditiveAnimData->Duration)
			{
				AdditiveBlendState.CurrentAnimTime = FMath::Fmod(AdditiveBlendState.CurrentAnimTime,
				                                                 AdditiveAnimData->Duration);
			}
			else if (!AdditiveAnimData->bLooping)
			{
				AdditiveBlendState.CurrentAnimTime = FMath::Min(AdditiveBlendState.CurrentAnimTime,
				                                                AdditiveAnimData->Duration);
			}

			// Update blend
			AdditiveBlendState.CurrentBlendTime += DeltaTime;

			// For additive states, we're either blending in or out
			if (AdditiveBlendState.BlendAlpha < 1.0f)
			{
				// Blending in
				AdditiveBlendState.BlendAlpha = FMath::Clamp(
					AdditiveBlendState.CurrentBlendTime / AdditiveBlendState.BlendTime, 0.0f, 1.0f);
			}
			else if (AdditiveBlendState.bIsMarkedRemove)
			{
				// Blending out
				AdditiveBlendState.BlendAlpha = 1.0f - FMath::Clamp(
					AdditiveBlendState.CurrentBlendTime / AdditiveBlendState.BlendTime, 0.0f, 1.0f);

				// If blend out is complete and state is marked for removal, remove it
				if (AdditiveBlendState.BlendAlpha <= 0.0f && AdditiveBlendState.bIsMarkedRemove)
				{
					StatesToRemove.Add(AdditiveState);
				}
			}
		}
	}

	// Remove completed states
	for (EWeaponAnimState StateToRemove : StatesToRemove)
	{
		ActiveAdditiveStates.Remove(StateToRemove);
	}
}

EWeaponAnimState UProceduralWeaponAnimation::GetCurrentAnimationState() const
{
	return BlendState.CurrentState;
}

EWeaponAnimState UProceduralWeaponAnimation::GetTargetAnimationState() const
{
	return BlendState.TargetState;
}

bool UProceduralWeaponAnimation::IsAdditiveStateActive(EWeaponAnimState State) const
{
	return ActiveAdditiveStates.Contains(State) && ActiveAdditiveStates[State].BlendAlpha > 0.0f;
}

FTransform UProceduralWeaponAnimation::GetCalculatedTransform() const
{
	return CalculatedTransform;
}

FTransform UProceduralWeaponAnimation::SampleCurve(const FWeaponAnimData& AnimData, float Time)
{
	FTransform ResultTransform = FTransform::Identity;

	// Clamp time to duration
	float ClampedTime = FMath::Clamp(Time, 0.0f, AnimData.Duration);

	// Sample the location curve
	if (AnimData.LocationCurve)
	{
		FVector LocationValue = AnimData.LocationCurve->GetVectorValue(ClampedTime);
		ResultTransform.SetTranslation(LocationValue);
	}

	// Sample the rotation curve
	if (AnimData.RotationCurve)
	{
		FVector RotationValue = AnimData.RotationCurve->GetVectorValue(ClampedTime);
		ResultTransform.SetRotation(FQuat::MakeFromEuler(RotationValue));
	}

	return ResultTransform;
}

void UProceduralWeaponAnimation::CalculateTransform()
{
	// Start with identity transform
	FTransform CurrentTransform = FTransform::Identity;

	// If we have a valid current state animation
	if (const FWeaponAnimData* CurrentAnimData = AnimationMap.Find(BlendState.CurrentState))
	{
		CurrentTransform = SampleCurve(*CurrentAnimData, BlendState.CurrentAnimTime);
	}

	// If we're blending to a target state
	if (BlendState.BlendAlpha > 0.0f && BlendState.BlendAlpha < 1.0f)
	{
		if (const FWeaponAnimData* TargetAnimData = AnimationMap.Find(BlendState.TargetState))
		{
			// Sample target animation curve
			FTransform TargetTransform = SampleCurve(*TargetAnimData, BlendState.TargetAnimTime);

			// Blend between current and target transforms
			CurrentTransform = UKismetMathLibrary::TLerp(CurrentTransform, TargetTransform, BlendState.BlendAlpha);
		}
	}

	// Apply additive animations
	for (const auto& Pair : ActiveAdditiveStates)
	{
		const EWeaponAnimState AdditiveState = Pair.Key;
		const FWeaponAnimBlendState& AdditiveBlendState = Pair.Value;
		UE_LOG(LogTemp, Display, TEXT("Active additive state in PWA: %hhd"), AdditiveState);

		if (const FWeaponAnimData* AdditiveAnimData = AnimationMap.Find(AdditiveState))
		{
			if (AdditiveAnimData->bAdditive)
			{
				// Sample additive animation curve
				FTransform AdditiveTransform = SampleCurve(*AdditiveAnimData, AdditiveBlendState.CurrentAnimTime);

				// Scale the additive transform by the blend alpha
				/*AdditiveTransform.SetScale3D(AdditiveTransform.GetScale3D() * AdditiveBlendState.BlendAlpha);
				AdditiveTransform.SetRotation(FQuat::Slerp(FQuat::Identity, AdditiveTransform.GetRotation(), AdditiveBlendState.BlendAlpha));
				AdditiveTransform.SetTranslation(AdditiveTransform.GetTranslation() * AdditiveBlendState.BlendAlpha);*/

				// Apply additive transform
				//CurrentTransform = CurrentTransform * AdditiveTransform;
				CurrentTransform = UKismetMathLibrary::ComposeTransforms(CurrentTransform, AdditiveTransform);
			}
		}
	}

	// Store the calculated transform
	CalculatedTransform = CurrentTransform;
}
